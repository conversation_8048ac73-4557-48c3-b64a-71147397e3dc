{"action6": {"actor": "system", "website": "https://www.kayak.com/ugtm/flights", "_recorded_at": "2025-08-12T14:56:54.521Z", "action": "navigate", "delay_since_last_ms": 53, "sleep_after": 1000, "synchronize": "true"}, "action7": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:55.099Z", "action": "navigate", "delay_since_last_ms": 578, "sleep_after": 1000, "synchronize": "true"}, "action8": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:55.211Z", "action": "navigate", "delay_since_last_ms": 112, "sleep_after": 1000, "synchronize": "true"}, "action9": {"actor": "system", "website": "https://www.googletagmanager.com/static/service_worker/5840/sw_iframe.html?origin=https%3A%2F%2Fwww.kayak.com", "_recorded_at": "2025-08-12T14:56:55.421Z", "action": "navigate", "delay_since_last_ms": 210, "sleep_after": 1000, "synchronize": "true"}, "action13": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:56.010Z", "action": "navigate", "delay_since_last_ms": 17, "sleep_after": 1000, "synchronize": "true"}, "action14": {"actor": "system", "website": "https://5142311.fls.doubleclick.net/activityi;dc_pre=CIL94YLEhY8DFSGq_QcdVrIw6w;src=5142311;type=visit0;cat=front0;ord=************;npa=0;auiddc=1967137715.1755010615;u12=hdY9QAg1iWN6fOKjhxGg5yFg2kA;u13=21CfZGJ6Q9xpy6wmLpM5yos4pFc;u16=en_US;u27=21CfZGJ6Q9xpy6wmLpM5yos4pFc;u28=flight;u29=kayak;uaa=arm;uab=64;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.185%7CGoogle%2520Chrome%3B138.0.7204.185;uamb=0;uam=;uap=macOS;uapv=15.6.0;uaw=0;pscdl=noapi;frm=1;_tu=KlA;gtm=45fe5870v9190574211z876415799za200zb76415799zd76415799;gcs=G111;gcd=13t3t3t3t5l1;dma=0;dc_fmt=2;tcfd=10000;tag_exp=101509157~103116026~103200004~103233427~104527906~104528501~104684208~104684211~104948813~105033766~105033768~105103161~105103163~105113532~105135708~105135710~105231380~105231382;epver=2;dc_random=1755010615481;_dc_test=1;~oref=https%3A%2F%2Fwww.kayak.com%2Fflights?", "_recorded_at": "2025-08-12T14:56:56.041Z", "action": "navigate", "delay_since_last_ms": 31, "sleep_after": 1000, "synchronize": "true"}, "action15": {"actor": "system", "website": "https://td.doubleclick.net/td/rul/988306736?random=1755010615992&cv=11&fst=1755010615992&fmt=3&bg=ffffff&guid=ON&async=1&en=purchase&gcl_ctr=2&gtm=45be5870v867588557z876415799za200zb76415799zd76415799xea&gcs=G111&gcd=13t3t3t3t5l1&dma=0&tcfd=10000&tag_exp=101509157~102015666~103116026~103200004~103233427~104527906~104528501~104684208~104684211~104948813~105033763~105033765~105103161~105103163~105113532~105135708~105135710~105231383~105231385&u_w=1280&u_h=720&url=https%3A%2F%2Fwww.kayak.com%2Fflights&label=undefined&hn=www.googleadservices.com&frm=1&tiba=Cheap%20Flights%2C%20Airline%20Tickets%20%26%20Airfare%20Deals%20%7C%20KAYAK&value=0&currency_code=USD&bttype=purchase&npa=0&pscdl=noapi&auid=1967137715.1755010615&uaa=arm&uab=64&uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.185%7CGoogle%2520Chrome%3B138.0.7204.185&uamb=0&uam=&uap=macOS&uapv=15.6.0&uaw=0&fledge=1&capi=1&_tu=Kg&item=(****)&ct_cookie_present=0", "_recorded_at": "2025-08-12T14:56:56.102Z", "action": "navigate", "delay_since_last_ms": 61, "sleep_after": 1000, "synchronize": "true"}, "action16": {"actor": "system", "website": "https://td.doubleclick.net/td/rul/988306736?random=1755010615999&cv=11&fst=1755010615999&fmt=3&bg=ffffff&guid=ON&async=1&gtm=45be5870v867588557z876415799za200zb76415799zd76415799xea&gcd=13t3t3t3t5l1&dma=0&tcfd=10000&tag_exp=101509157~102015666~103116026~103200004~103233427~104527906~104528501~104684208~104684211~104948813~105033763~105033765~105103161~105103163~105113532~105135708~105135710~105231383~105231385&u_w=1280&u_h=720&url=https%3A%2F%2Fwww.kayak.com%2Fflights&hn=www.googleadservices.com&frm=1&tiba=Cheap%20Flights%2C%20Airline%20Tickets%20%26%20Airfare%20Deals%20%7C%20KAYAK&npa=0&pscdl=noapi&auid=1967137715.1755010615&uaa=arm&uab=64&uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.185%7CGoogle%2520Chrome%3B138.0.7204.185&uamb=0&uam=&uap=macOS&uapv=15.6.0&uaw=0&fledge=1&_tu=Cg&data=event_name%3DscreenView%3Bgoogle_business_vertical%3Dflights", "_recorded_at": "2025-08-12T14:56:56.104Z", "action": "navigate", "delay_since_last_ms": 2, "sleep_after": 1000, "synchronize": "true"}, "action17": {"actor": "system", "website": "https://cm.g.doubleclick.net/partnerpixels?gdpr_consent=CQWCSgAQWCSgAGdABBENB3FgAAAAAEPgACiQAAAVzgSwAKAAsABoAEAAKgAXAAyABwAEAAJwAVAAyAByAEUAJgATgApABVAC2AF8AMIAfgBAACcAFaAMuAaIBpADuAIQARkAloBdQDAAGnAXmAywB_gFcwAA&gdpr=0&url=https%3A%2F%2Fwww.kayak.com%2Fflights", "_recorded_at": "2025-08-12T14:56:56.242Z", "action": "navigate", "delay_since_last_ms": 138, "sleep_after": 1000, "synchronize": "true"}, "action18": {"actor": "system", "website": "https://securepubads.g.doubleclick.net/static/topics/topics_frame.html", "_recorded_at": "2025-08-12T14:56:56.318Z", "action": "navigate", "delay_since_last_ms": 76, "sleep_after": 1000, "synchronize": "true"}, "action19": {"actor": "system", "website": "https://d4d5e9ced6cf9bdeffef86ff55415c83.safeframe.googlesyndication.com/safeframe/1-0-45/html/container.html", "_recorded_at": "2025-08-12T14:56:56.398Z", "action": "navigate", "delay_since_last_ms": 80, "sleep_after": 1000, "synchronize": "true"}, "action10": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:55.938Z", "action": "navigate", "delay_since_last_ms": 517, "sleep_after": 1000, "synchronize": "true"}, "action11": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:55.941Z", "action": "navigate", "delay_since_last_ms": 3, "sleep_after": 1000, "synchronize": "true"}, "action23.2.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.219Z", "parent_action": "action23.2", "action": "getByPlaceHolder", "params": ["To?"], "event": "click", "delay_since_last_ms": 0}, "action12": {"actor": "system", "website": "https://td.doubleclick.net/td/rul/undefined?random=1755010615688&cv=11&fst=1755010615688&fmt=3&bg=ffffff&guid=ON&async=1&en=purchase&gcl_ctr=1&gtm=45be5870z876415799za200zb76415799zd76415799xea&gcs=G111&gcd=13t3t3t3t5l1&dma=0&tcfd=10000&tag_exp=101509157~103116026~103200004~103233427~104527906~104528500~104684208~104684211~104948813~105033763~105033765~105103161~105103163~105113532~105135708~105135710&u_w=1280&u_h=720&url=https%3A%2F%2Fwww.kayak.com%2Fflights&label=undefined&hn=www.googleadservices.com&frm=1&tiba=Cheap%20Flights%2C%20Airline%20Tickets%20%26%20Airfare%20Deals%20%7C%20KAYAK&value=0&currency_code=USD&bttype=purchase&npa=0&pscdl=noapi&auid=1967137715.1755010615&uaa=arm&uab=64&uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.185%7CGoogle%2520Chrome%3B138.0.7204.185&uamb=0&uam=&uap=macOS&uapv=15.6.0&uaw=0&fledge=1&capi=1&_tu=Kg&item=(****)&ct_cookie_present=0", "_recorded_at": "2025-08-12T14:56:55.993Z", "action": "navigate", "delay_since_last_ms": 52, "sleep_after": 1000, "synchronize": "true"}, "action2*******": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.218Z", "parent_action": "action23.1.1", "action": "getByRole", "params": ["list"], "event": "click", "delay_since_last_ms": 1}, "action23.7.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.222Z", "parent_action": "action23.7", "action": "getByRole", "params": ["radio"], "event": "click", "delay_since_last_ms": 1}, "action23.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.215Z", "parent_action": "action23", "action": "getByRole", "params": ["menuitem"], "event": "click", "delay_since_last_ms": 0}, "action23.2": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.219Z", "parent_action": "action23", "action": "locator", "params": ["div#main-search-form > section.E9x1 > div.E9x1-card > div.E9x1-main-content > div.E9x1-container > div.W5IJ.W5IJ-mod-limit-width:nth-child(1) > div > div.J_T2 > div.ITMZ.ITMZ-mod-delay-short.ITMZ-mod-animate-up:nth-child(2) > div.J_T2-row.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-with-bg.J_T2-mod-with-shadow.J_T2-mod-rounding-medium:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-grow.J_T2-mod-no-min-width.J_T2-mod-with-divider:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-m.J_T2-mod-spacing-y-none.J_T2-mod-grow.J_T2-mod-no-min-width.J_T2-mod-divider-inner.N4mz-location-group > div:nth-child(1) > div.pM26.pM26-mod-multi-value > div.xAR_.xAR_-mod-padding-x-base.xAR_-mod-taller-l.MT35 > input.AQWr-mod-padding-left-none.AQWr-mod-padding-right-none.NhpT.NhpT-mod-radius-none.NhpT-mod-corner-radius-all.NhpT-mod-size-large.NhpT-mod-state-hover.NhpT-mod-text-overflow-ellipsis.NhpT-mod-theme-search.NhpT-mod-validation-state-neutral.NhpT-mod-validation-style-border.NhpT-mod-bricks.NhpT-mod-reset-default-width.NhpT-mod-full-width-height"], "delay_since_last_ms": 1}, "action23.3": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.219Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1) > div.sGVi.sGVi-dropdown-content > div:nth-child(1) > div.T-oO.T-oO-mod-margin-bottom-base > div.T-oO-flex-variants:nth-child(1) > div.vlBx.vlBx-pres-content-a11y.vlBx-mod-spacing-default.vlBx-mod-size-default.vlBx-mod-variant-bold > span.c2jKu.c2jKu-pres-content-a11y.vlBx-item:nth-child(1) > label.c2jKu-label.c2jKu-mod-border-variant-regular.c2jKu-checked"], "delay_since_last_ms": 0}, "action25.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.221Z", "parent_action": "action25", "action": "getByRole", "params": ["button"], "event": "click", "delay_since_last_ms": 0}, "action23.4": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.220Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1) > div.sGVi.sGVi-dropdown-content > div.OV9e:nth-child(2) > div.OV9e-cal-wrapper:nth-child(1) > div.OV9e-tbl-wrapper > div:nth-child(3) > table.or3C.or3C-wrapper > tbody > tr.or3C-week.or3C-grid:nth-child(1) > td.vn3g.vn3g-t-selected.vn3g-hover.vn3g-r-full:nth-child(3) > div.vn3g-button"], "delay_since_last_ms": 0}, "action23.5": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.221Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1) > div.sGVi.sGVi-dropdown-content > div.OV9e:nth-child(2) > div.OV9e-cal-wrapper:nth-child(1) > div.OV9e-tbl-wrapper > div:nth-child(3) > table.or3C.or3C-wrapper > tbody > tr.or3C-week.or3C-grid:nth-child(1) > td.vn3g.vn3g-t-selected-end.vn3g-hover.vn3g-r-end.vn3g-p-fuse-start:nth-child(4) > div.vn3g-button"], "delay_since_last_ms": 0}, "action23.2.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.219Z", "parent_action": "action23.2.1", "action": "getByRole", "params": ["group"], "event": "click", "delay_since_last_ms": 0}, "action23.6": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.221Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-mod-animated.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1)"], "delay_since_last_ms": 0}, "action24": {"actor": "system", "role": "", "_recorded_at": "2025-08-12T14:57:15.218Z", "option_values": ["Cairo (CAI)"], "options": ["setName"], "action": "getByText", "params": ["div#main-search-form > section.E9x1 > div.E9x1-card > div.E9x1-main-content > div.E9x1-container > div.W5IJ.W5IJ-mod-limit-width:nth-child(1) > div > div.J_T2 > div.ITMZ.ITMZ-mod-delay-short.ITMZ-mod-animate-up:nth-child(2) > div.J_T2-row.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-with-bg.J_T2-mod-with-shadow.J_T2-mod-rounding-medium:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-grow.J_T2-mod-no-min-width.J_T2-mod-with-divider:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-m.J_T2-mod-spacing-y-none.J_T2-mod-grow.J_T2-mod-no-min-width.J_T2-mod-divider-inner.N4mz-location-group > div:nth-child(1) > div.pM26.pM26-mod-multi-value > div.xAR_.xAR_-mod-padding-x-base.xAR_-mod-taller-l.MT35 > div.c_neb.c_neb-mod-theme-solid.c_neb-mod-direction-default.c_neb-mod-variant-default.c_neb-mod-ellipsis > div.c_neb-item > div.c_neb-item-value:nth-child(1)"], "event": "click", "delay_since_last_ms": 0}, "action25": {"actor": "system", "role": "button", "_recorded_at": "2025-08-12T14:57:15.221Z", "option_values": ["September 2, 2025 Prices on this day are around average"], "options": ["setName"], "action": "getByText", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1) > div.sGVi.sGVi-dropdown-content > div.OV9e:nth-child(2) > div.OV9e-cal-wrapper:nth-child(1) > div.OV9e-tbl-wrapper > div:nth-child(3) > table.or3C.or3C-wrapper > tbody > tr.or3C-week.or3C-grid:nth-child(1) > td.vn3g.vn3g-t-selected.vn3g-hover.vn3g-r-full:nth-child(3) > div.vn3g-button"], "event": "click", "delay_since_last_ms": 1}, "action26": {"actor": "system", "role": "button", "_recorded_at": "2025-08-12T14:57:15.221Z", "option_values": ["September 3, 2025. Selected as end date. Prices on this day are below average"], "options": ["setName"], "action": "getByText", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-visible > div.Gagx-content:nth-child(2) > div:nth-child(1) > div.sGVi.sGVi-dropdown-content > div.OV9e:nth-child(2) > div.OV9e-cal-wrapper:nth-child(1) > div.OV9e-tbl-wrapper > div:nth-child(3) > table.or3C.or3C-wrapper > tbody > tr.or3C-week.or3C-grid:nth-child(1) > td.vn3g.vn3g-t-selected-end.vn3g-hover.vn3g-r-end.vn3g-p-fuse-start:nth-child(4) > div.vn3g-button"], "event": "click", "delay_since_last_ms": 0}, "action27": {"actor": "system", "role": "radio", "_recorded_at": "2025-08-12T14:57:15.222Z", "option_values": ["Premium Economy"], "options": ["setName"], "action": "getByText", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-mod-animated.Gagx-visible.FqLu-mod-overflow-y > div.Gagx-content:nth-child(2) > span.AFFP.AFFP-body.AFFP-m.AFFP-res > div.EwZ1:nth-child(2) > div.vlBx.vlBx-pres-chip-alt.vlBx-mod-spacing-extra-small.vlBx-mod-size-small.vlBx-mod-variant-regular.vlBx-mod-wrap:nth-child(2) > span.c2jKu.c2jKu-pres-chip-alt.vlBx-item:nth-child(2) > label.c2jKu-label.c2jKu-mod-border-variant-regular"], "event": "click", "delay_since_last_ms": 0}, "action28": {"actor": "system", "role": "", "_recorded_at": "2025-08-12T14:57:15.222Z", "option_values": [""], "options": ["setName"], "action": "getByText", "params": ["input#id-p"], "event": "click", "delay_since_last_ms": 0}, "action29": {"actor": "user", "output": "__val_action29", "exit": "blur", "_recorded_at": "2025-08-12T14:57:15.222Z", "parent_action": "action28", "action": "fill", "params": [{"selector": "input#id-p", "text": ""}], "event": "fill", "delay_since_last_ms": 0, "synchronize": "true"}, "action23.*******": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.220Z", "parent_action": "action23.3.1.1", "action": "getByRole", "params": ["columnheader"], "event": "click", "delay_since_last_ms": 0}, "action23.10": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.223Z", "parent_action": "action23", "action": "locator", "params": ["div#main-search-form > section.E9x1 > div.E9x1-card > div.E9x1-main-content > div.E9x1-container > div.W5IJ.W5IJ-mod-limit-width:nth-child(1) > div > div.J_T2 > div.ITMZ.ITMZ-mod-delay-short.ITMZ-mod-animate-up:nth-child(2) > div.J_T2-row.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-with-bg.J_T2-mod-with-shadow.J_T2-mod-rounding-medium:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-with-divider:nth-child(3) > span.AFFP.AFFP-body.AFFP-m.AFFP-res > div.udzg.udzg-mod-size-base.udzg-mod-radius-none.udzg-mod-state-default.udzg-mod-alignment-left.udzg-pres-default.udzg-mod-variant-default.udzg-mod-active"], "delay_since_last_ms": 0}, "action20": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:56.465Z", "action": "navigate", "delay_since_last_ms": 67, "sleep_after": 1000, "synchronize": "true"}, "actions": ["action0", "action1", "action2", "action3", "action4", "action5", "action6", "action7", "action8", "action9", "action10", "action11", "action12", "action13", "action14", "action15", "action16", "action17", "action18", "action19", "action20", "action21", "action22", "action23", "action23.1", "action23.1.1", "action2*******", "action2*******.1", "action2*******.1.1", "action24", "action23.2", "action23.2.1", "action23.2.1.1", "action23.3", "action23.3.1", "action23.3.1.1", "action23.*******", "action23.*******.1", "action23.*******.1.1", "action23.4", "action25", "action25.1", "action23.5", "action26", "action23.6", "action23.7", "action23.7.1", "action23.8", "action27", "action28", "action29", "action29.1", "action29.1.1", "action23.9", "action23.10", "action30"], "action21": {"actor": "system", "website": "https://www.google.com/recaptcha/api2/aframe", "_recorded_at": "2025-08-12T14:57:15.211Z", "action": "navigate", "delay_since_last_ms": 18746, "sleep_after": 1000, "synchronize": "true"}, "action22": {"actor": "system", "website": "https://ep2.adtrafficquality.google/sodar/sodar2/237/runner.html", "_recorded_at": "2025-08-12T14:57:15.212Z", "action": "navigate", "delay_since_last_ms": 1, "sleep_after": 1000, "synchronize": "true"}, "action23.3.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.220Z", "parent_action": "action23.3.1", "action": "getByRole", "params": ["columnheader"], "event": "click", "delay_since_last_ms": 1}, "action23": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:57:15.215Z", "action": "navigate", "delay_since_last_ms": 3, "sleep_after": 1000, "synchronize": "true"}, "action23.*******.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.220Z", "parent_action": "action23.*******.1", "action": "getByRole", "params": ["button"], "event": "click", "delay_since_last_ms": 0}, "action23.7": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.221Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-mod-animated.Gagx-visible > div.Gagx-content:nth-child(2) > span.AFFP.AFFP-body.AFFP-m.AFFP-res > div.cvdH.cvdH-mod-spacing-base:nth-child(1) > div.FkqV:nth-child(2) > div.FkqV-inner > div.T_3c > input.T_3c-input"], "delay_since_last_ms": 0}, "action23.8": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.222Z", "parent_action": "action23", "action": "locator", "params": ["body.react.react-st.en_US.wide.wide-fd.a11y-focus-outlines > div.FqLu.FqLu-mod-layer-dropdown:nth-child(42) > div.Gagx.Gagx-mod-radius-medium.Gagx-mod-animated.Gagx-visible.FqLu-mod-overflow-y > div.Gagx-content:nth-child(2) > span.AFFP.AFFP-body.AFFP-m.AFFP-res > div.EwZ1:nth-child(2) > div.vlBx.vlBx-pres-chip-alt.vlBx-mod-spacing-extra-small.vlBx-mod-size-small.vlBx-mod-variant-regular.vlBx-mod-wrap:nth-child(2) > span.c2jKu.c2jKu-pres-chip-alt.vlBx-item:nth-child(2) > label.c2jKu-label.c2jKu-mod-border-variant-regular"], "delay_since_last_ms": 0}, "action29.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.223Z", "parent_action": "action29", "action": "getByRole", "params": ["presentation"], "event": "click", "delay_since_last_ms": 1}, "action23.9": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.223Z", "parent_action": "action23", "action": "locator", "params": ["div#main-search-form > section.E9x1 > div.E9x1-card > div.E9x1-main-content > div.E9x1-container > div.W5IJ.W5IJ-mod-limit-width:nth-child(1) > div > div.J_T2 > div.ITMZ.ITMZ-mod-delay-short.ITMZ-mod-animate-up:nth-child(2) > div.J_T2-row.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none.J_T2-mod-with-bg.J_T2-mod-with-shadow.J_T2-mod-rounding-medium:nth-child(1) > div.J_T2-field-group.J_T2-mod-collapse-l.J_T2-mod-spacing-y-none.J_T2-mod-spacing-x-none:nth-child(4) > div.c9HET > button.RxNS.RxNS-mod-stretch.RxNS-mod-animation-search.RxNS-mod-variant-solid.RxNS-mod-theme-progress.RxNS-mod-shape-default.RxNS-mod-spacing-base.RxNS-mod-size-medium"], "delay_since_last_ms": 0}, "action30": {"actor": "system", "role": "", "_recorded_at": "2025-08-12T14:57:15.223Z", "option_values": ["Compare flight deals from 100s of sites.\nFlights\nStays\nCars\nPackages\nKAYAK.ai\nRound-trip\n0 bags\nCAI\nSPX\nJeddah, Saudi Arabia (JED)\nTue 9/2\n–\nWed 9/3\n1 adult, Premium\nCompare vs. KAYAK\nSaudia\nDirect flights only"], "options": ["setName"], "action": "getByText", "params": ["div#main-search-form > section.E9x1 > div.E9x1-card > div.E9x1-main-content > div.E9x1-container > div.W5IJ.W5IJ-mod-limit-width:nth-child(1)"], "event": "click", "delay_since_last_ms": 0}, "action23.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.217Z", "parent_action": "action23.1", "action": "getByRole", "params": ["combobox"], "event": "click", "delay_since_last_ms": 2}, "action23.3.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.219Z", "parent_action": "action23.3", "action": "getByRole", "params": ["row"], "event": "click", "delay_since_last_ms": 0}, "action23.*******.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.220Z", "parent_action": "action23.*******", "action": "getByRole", "params": ["row"], "event": "click", "delay_since_last_ms": 0}, "action2*******.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.218Z", "parent_action": "action2*******.1", "action": "getByRole", "params": ["button"], "event": "click", "delay_since_last_ms": 0}, "action2*******.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.218Z", "parent_action": "action2*******", "action": "getByRole", "params": ["listitem"], "event": "click", "delay_since_last_ms": 0}, "action29.1.1": {"actor": "system", "_recorded_at": "2025-08-12T14:57:15.223Z", "parent_action": "action29.1", "action": "getByRole", "params": ["spinbutton"], "event": "click", "delay_since_last_ms": 0}, "action0": {"actor": "system", "website": "https://www.kayak.com/flights", "_recorded_at": "2025-08-12T14:56:52.542Z", "action": "navigate", "delay_since_last_ms": 63347, "sleep_after": 1000, "synchronize": "true"}, "action1": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:53.904Z", "action": "navigate", "delay_since_last_ms": 1362, "sleep_after": 1000, "synchronize": "true"}, "action2": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:53.995Z", "action": "navigate", "delay_since_last_ms": 91, "sleep_after": 1000, "synchronize": "true"}, "action3": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:54.024Z", "action": "navigate", "delay_since_last_ms": 29, "sleep_after": 1000, "synchronize": "true"}, "action4": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:54.466Z", "action": "navigate", "delay_since_last_ms": 442, "sleep_after": 1000, "synchronize": "true"}, "action5": {"actor": "system", "website": "about:blank", "_recorded_at": "2025-08-12T14:56:54.468Z", "action": "navigate", "delay_since_last_ms": 2, "sleep_after": 1000, "synchronize": "true"}}