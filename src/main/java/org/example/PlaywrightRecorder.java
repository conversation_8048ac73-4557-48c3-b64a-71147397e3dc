package org.example;

import com.microsoft.playwright.*;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.FileWriter;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;

public class PlaywrightRecorder {

    private final JSONArray actionsOrder = new JSONArray();
    private final Map<String, JSONObject> actionObjects = new LinkedHashMap<>();

    private int mainCounter = 0;
    private final Map<String, Integer> subActionCounters = new HashMap<>();

    private long lastTimestamp = System.currentTimeMillis();

    // Last recorded action key, to track parent-child relations
    private String lastMainActionKey = null;
    private String lastActionKey = null;

    private String nextMainActionKey() {
        String key = "action" + mainCounter;
        mainCounter++;
        // Reset sub counters for this main action
        subActionCounters.put(key, 0);
        return key;
    }

    private String nextSubActionKey(String parentKey) {
        int count = subActionCounters.getOrDefault(parentKey, 0) + 1;
        subActionCounters.put(parentKey, count);
        return parentKey + "." + count;
    }

    private void recordEventFromJS(JSONObject event) {
        long now = System.currentTimeMillis();
        long delay = Math.max(0, now - lastTimestamp);
        lastTimestamp = now;

        String type = event.optString("type", "unknown");
        String actionKey;
        JSONObject obj = new JSONObject();

        switch (type.toLowerCase()) {
            case "navigation": {
                actionKey = nextMainActionKey();
                obj.put("actor", "system");
                obj.put("action", "navigate");
                obj.put("website", event.optString("url", ""));
                obj.put("sleep_after", 1000);  // Longer sleep for navigation
                obj.put("synchronize", "true");

                lastMainActionKey = actionKey;
                lastActionKey = actionKey;
                break;
            }

            case "locator": {
                // Nested under last main action (like navigation)
                if (lastMainActionKey == null) {
                    actionKey = nextMainActionKey();
                    lastMainActionKey = actionKey;
                } else {
                    actionKey = nextSubActionKey(lastMainActionKey);
                }
                obj.put("actor", "system");
                obj.put("action", "locator");
                obj.put("params", new JSONArray().put(event.optString("selector", "")));
                obj.put("parent_action", lastMainActionKey);

                lastActionKey = actionKey;
                break;
            }

            case "getbyrole": {
                if (lastActionKey == null) {
                    actionKey = nextMainActionKey();
                    lastMainActionKey = actionKey;
                } else {
                    actionKey = nextSubActionKey(lastActionKey);
                }
                obj.put("actor", "system");
                obj.put("action", "getByRole");
                obj.put("params", new JSONArray().put(event.optString("role", "")));
                obj.put("parent_action", lastActionKey);
                if (event.has("event")) obj.put("event", event.getString("event"));

                lastActionKey = actionKey;
                break;
            }

            case "getbyplaceholder": {
                if (lastActionKey == null) {
                    actionKey = nextMainActionKey();
                    lastMainActionKey = actionKey;
                } else {
                    actionKey = nextSubActionKey(lastActionKey);
                }
                obj.put("actor", "system");
                obj.put("action", "getByPlaceHolder");
                obj.put("params", new JSONArray().put(event.optString("placeholder", "")));
                obj.put("event", "click");
                obj.put("parent_action", lastActionKey);

                lastActionKey = actionKey;
                break;
            }

            case "getbylabel": {
                if (lastActionKey == null) {
                    actionKey = nextMainActionKey();
                    lastMainActionKey = actionKey;
                } else {
                    actionKey = nextSubActionKey(lastActionKey);
                }
                obj.put("actor", "system");
                obj.put("action", "getByLabel");
                obj.put("params", new JSONArray().put(event.optString("label", "")));
                obj.put("event", "click");
                obj.put("sleep_after", 400);
                obj.put("parent_action", lastActionKey);

                lastActionKey = actionKey;
                break;
            }

            case "fill":
            case "input": {
                // User fill action, normally nested under last system action
                actionKey = nextMainActionKey();

                obj.put("actor", "user");
                obj.put("action", "fill");
                JSONObject params = new JSONObject();
                params.put("selector", event.optString("selector", ""));
                params.put("text", event.optString("value", ""));
                obj.put("params", new JSONArray().put(params));
                obj.put("event", "fill");
                obj.put("output", "__val_" + actionKey);
                obj.put("exit", "blur");
                obj.put("synchronize", "true");

                // Try to assign a parent_action if lastActionKey is system
                if (lastActionKey != null) {
                    JSONObject lastObj = actionObjects.get(lastActionKey);
                    if (lastObj != null && "system".equals(lastObj.optString("actor"))) {
                        obj.put("parent_action", lastActionKey);
                    }
                }

                lastActionKey = actionKey;
                break;
            }

            case "click": {
                actionKey = nextMainActionKey();
                obj.put("actor", "system");
                obj.put("action", "getByText");
                obj.put("params", new JSONArray().put(event.optString("selector", "")));
                obj.put("event", "click");

                if (event.has("role")) {
                    obj.put("role", event.getString("role"));
                }
                if (event.has("name")) {
                    obj.put("options", new JSONArray().put("setName"));
                    obj.put("option_values", new JSONArray().put(event.getString("name")));
                }
                obj.put("delay_since_last_ms", delay);
                obj.put("_recorded_at", Instant.ofEpochMilli(now).toString());

                lastActionKey = actionKey;
                break;
            }

            case "blur": {
                // Optionally record blur events for fill exits, you can omit if not needed
                // Not always necessary to create a separate action for blur, so ignoring here
                return;
            }

            default: {
                // Default fallback - treat as a system click or getByText
                actionKey = nextMainActionKey();
                obj.put("actor", "system");
                obj.put("action", "getByText");
                obj.put("params", new JSONArray().put(event.optString("selector", "")));
                obj.put("event", event.optString("type", "click"));

                lastActionKey = actionKey;
                break;
            }
        }

        obj.put("delay_since_last_ms", delay);
        obj.put("_recorded_at", Instant.ofEpochMilli(now).toString());

        actionObjects.put(actionKey, obj);
        actionsOrder.put(actionKey);

        System.out.println("Recorded " + actionKey + " : " + obj.toString(2));
    }

    private void writeOut(String fileName) {
        try (FileWriter fw = new FileWriter(fileName)) {
            JSONObject root = new JSONObject();
            root.put("actions", actionsOrder);
            for (Map.Entry<String, JSONObject> e : actionObjects.entrySet()) {
                root.put(e.getKey(), e.getValue());
            }
            fw.write(root.toString(2));
            System.out.println("Saved JSON to " + fileName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void run(String startUrl) {
        CountDownLatch latch = new CountDownLatch(1);

        try (Playwright pw = Playwright.create()) {
            Browser browser = pw.chromium().launch(new BrowserType.LaunchOptions()
                    .setChannel("chrome")
                    .setHeadless(false));
            BrowserContext ctx = browser.newContext();
            Page page = ctx.newPage();

            page.exposeBinding("recordEvent", (bindingSource, args) -> {
                if (args.length > 0 && args[0] instanceof String) {
                    String jsonStr = (String) args[0];
                    try {
                        JSONObject evt = new JSONObject(jsonStr);
                        recordEventFromJS(evt);
                    } catch (Exception ex) {
                        System.err.println("Bad event JSON from page: " + jsonStr);
                        ex.printStackTrace();
                    }
                } else {
                    System.err.println("recordEvent called with unexpected args: " + Arrays.toString(args));
                }
                return null;
            });

            String injection = """
                (() => {
                  if (window._playwrightRecorderInstalled) return;
                  window._playwrightRecorderInstalled = true;

                  function cssPath(el) {
                    if (!el) return "";
                    const parts = [];
                    while (el && el.nodeType === 1 && el.tagName.toLowerCase() !== 'html') {
                      let part = el.tagName.toLowerCase();
                      if (el.id) {
                        part += '#' + el.id;
                        parts.unshift(part);
                        break;
                      } else {
                        if (el.className && typeof el.className === 'string') {
                          const cls = el.className.trim().split(/\\s+/).join('.');
                          if (cls) part += '.' + cls;
                        }
                        const parent = el.parentElement;
                        if (parent) {
                          const siblings = Array.from(parent.children).filter(c => c.tagName === el.tagName);
                          if (siblings.length > 1) {
                            const index = Array.prototype.indexOf.call(parent.children, el) + 1;
                            part += `:nth-child(${index})`;
                          }
                        }
                      }
                      parts.unshift(part);
                      el = el.parentElement;
                    }
                    return parts.join(' > ');
                  }

                  function safeText(el) {
                    if (!el) return '';
                    return el.getAttribute('aria-label') || el.innerText || el.value || el.getAttribute('title') || '';
                  }

                  function emit(event) {
                    try {
                      window.recordEvent(JSON.stringify(event));
                    } catch (e) {}
                  }

                  // Listen for navigation changes (including pushState)
                  (function(history) {
                    const push = history.pushState;
                    history.pushState = function(state) {
                      const res = push.apply(this, arguments);
                      emit({type: 'navigation', url: location.href, timestamp: Date.now()});
                      return res;
                    };
                  })(window.history);

                  // Initial navigation event
                  emit({type:'navigation', url:location.href, timestamp: Date.now()});

                  // Click handler - emits click and attempts to classify element role
                  document.addEventListener('click', (ev) => {
                    const target = ev.target;
                    if (!target) return;
                    const selector = cssPath(target);
                    const name = safeText(target);
                    const role = target.getAttribute('role') || '';
                    // emit click
                    emit({type: 'click', selector, name, role, timestamp: Date.now()});
                  }, true);

                  // Input handler - emits fill/input with value
                  document.addEventListener('input', (ev) => {
                    const target = ev.target;
                    if (!target) return;
                    const selector = cssPath(target);
                    emit({type: 'input', selector, value: target.value, timestamp: Date.now()});
                  }, true);

                  // Blur handler - we do not emit separate blur events for simplicity

                  // Extra: When element focused, emit locator or getBy* events by heuristics
                  document.addEventListener('focusin', (ev) => {
                    const target = ev.target;
                    if (!target) return;
                    const selector = cssPath(target);

                    // Heuristics for getByPlaceHolder
                    if (target.placeholder) {
                      emit({type: 'getByPlaceHolder', placeholder: target.placeholder, selector, timestamp: Date.now()});
                    }
                    // Heuristics for getByLabel (assuming labels linked by id)
                    else {
                      // try to find label text if possible
                      let labelText = '';
                      if (target.id) {
                        const label = document.querySelector(`label[for="${target.id}"]`);
                        if (label) labelText = label.innerText || '';
                      }
                      if (labelText) {
                        emit({type: 'getByLabel', label: labelText.trim(), selector, timestamp: Date.now()});
                      } else {
                        // fallback emit locator event
                        emit({type: 'locator', selector, timestamp: Date.now()});
                      }
                    }
                  }, true);

                  // GetByRole heuristic on mouseover or focus - example only
                  document.addEventListener('mouseover', (ev) => {
                    const target = ev.target;
                    if (!target) return;
                    const selector = cssPath(target);
                    const role = target.getAttribute('role');
                    if (role) {
                      emit({type: 'getByRole', role, selector, event: 'click', timestamp: Date.now()});
                    }
                  }, true);

                })();
                """;

            page.addInitScript(injection);
            page.navigate(startUrl);
            page.evaluate(injection);

            // Listen for page close event
            page.onClose(p -> latch.countDown());

            System.out.println("Recorder running. Interact with the opened browser window.");
            System.out.println("Press ENTER in this terminal to stop and save, or close the page/window.");

            Thread consoleWatcher = new Thread(() -> {
                try {
                    int read = System.in.read();
                    if (read >= 0) {
                        if (!page.isClosed()) page.close();
                    }
                } catch (IOException ignored) {}
            });
            consoleWatcher.setDaemon(true);
            consoleWatcher.start();

            latch.await(); // Wait until page closes

            writeOut("recorded_actions.json");

            ctx.close();
            browser.close();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println("Recorder finished.");
        }
    }

    public static void main(String[] args) {
        String url = args.length > 0 ? args[0] : "https://www.kayak.com/flights";
        new PlaywrightRecorder().run(url);
    }
}